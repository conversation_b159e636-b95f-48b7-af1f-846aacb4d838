<app-splash-screen></app-splash-screen>
<!-- Temporary SW Update Test Buttons -->
<div style="position: fixed; top: 10px; right: 10px; z-index: 9999; background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
  <h4>SW Update Test</h4>
  <button (click)="testFooterDialog()" style="margin: 5px; padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">
    Test Footer Dialog
  </button>
  <br>
  <button (click)="testCloseDialog()" style="margin: 5px; padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">
    Close Footer Dialog
  </button>
</div>

<div
  [ngClass]="{
    'mobile-screen': deviceInfoModel.isMobile,
    'tablet-screen': deviceInfoModel.isTablet,
    'desktop-screen': deviceInfoModel.isDesktop
  }">
  <router-outlet></router-outlet>
</div>

<!-- <p-dialog
  header="New Version Update"
  [(visible)]="showSwUpdateDialog"
  [breakpoints]="{ '960px': '75vw', '640px': '100vw' }"
  [style]="{ width: '70vw', position: 'fixed', bottom: '40px' }"
  styleClass="p-box">
  The newer version of the application is available now.
  <a (click)="closeSwDialog()">Click here</a> to download it now
</p-dialog> -->

<!-- <dialog [open]="showSwUpdateDialog" [style]="{ width: '70vw', position: 'fixed', bottom: '40px' }">
  <p>The newer version of the application is available now.</p>
  <button (click)="closeSwDialog()">Update</button>
</dialog> -->
